payload {'model': 'Qwen3-235B-A22B', 'messages': [{'role': 'system', 'content': '标题修正任务:修复Markdown标题层级，遵循以下规则:\n1.格式:识别真正标题行、伪标题和无效标题\n2.层级:h1-h6连续不跳级，首行必为h1，同级缩进相同\n3.处理:"#数字、内容"合理转换，保留"#数.内容"的数字，删除空标题\n4.输出:直接返回JSON格式:\n{"corrections":[{"line_number":行号,"original_line":"原始内容","corrected_line":"修正内容","reason":"原因"}],"structure":[{"level":层级,"content":"内容","line_num":行号}]}'}, {'role': 'user', 'content': '1:# 中国移动\n3:# 仓储管理系统\n5:# 出库管理操作手册\n7:2025年1月\n13:# 目录\n15:1. 出库管理操作手册 31.1 出库业务流程图 31.1.1 出库单拣流程 31.1.2 波次出库流程 31.2 术语解释 42. 模块详细操作说明 42.1 出库订单 42.1.1 新增订单 42...\n17:2.3.1 新增波次单. 132.3.2 分配. 142.4 二次分拣. 152.4.1 发起二次分拣/分拣. 152.5 出库复核. 162.5.1 发起复核/复核. 172.6 出库打包. 182...\n19:# 1.出库管理操作手册\n21:# 1.1 出库业务流程图\n25:# 1.1.1 出库单拣流程\n29:# 1.1.2 波次出库流程\n33:# 1.2 术语解释\n51:# 2.模块详细操作说明\n53:# 2.1出库订单\n55:2.1出库订单作为出库流程的起点，该模块负责将客户订单转化为可执行的仓库任务，支持外部导入或系统手工创建，确保订单信息准确性和可追溯性。通过优先级设置和状态跟踪，优化订单处理顺序，减少人工干预错误，提...\n59:# 2.1.1新增订单\n71:# 2.1.2 审核/取消审核\n81:1. 点击【审核】按钮。\n83:2. 二次弹窗点击【确定】按钮，完成审核操作。\n91:# 2.1.3订单完结\n97:1. 在审核操作的基础上，点击【订单完结】按钮。\n99:2. 二次弹窗点击【确定】按钮后，完成订单完结操作。\n101:3. 完结后订单状态变为“关闭订单”，操作列不再展示按钮。\n107:# 2.1.4查看分配明细\n113:# 2.1.5 分配、一键分配/自动分配\n117:一键分配/自动分配：根据商品在商品管理中配置的周转规则和分配规则，自动执行分配，生成分配明细。\n123:1. 点击出库订单页操作列的【分配】按钮。\n125:2. 跳转至抹货分配页，点击商品tab的【分配】按钮。\n127:3. 抽屉弹窗“抹货分配”，点击【+手动分配】按钮，弹窗“分配库位”，选择库位后点击【保存】按钮\n129:4. 选择分配的库位信息在“抹货分配”中显示，点击【生成抹货任务】按钮，生成抹货任务号。\n131:5. 拣货任务号生成分配无误，点击【生成抹货单号】按钮后数据\n142:# 2.2 拣货管理\n148:# 2.2.1 分配拣货员\n156:1. 点击拣货管理页操作列【分配拣货员】按钮，跳转进“拣货任务分配”。2. “未分配”tab，勾选拣货任务号点击【手动分配拣货员】按钮。3. 下拉框选择拣货员，点击【确认】按钮完成分配拣货员操作。\n160:# 2.2.2取消抹货单\n170:# 2.2.3 拣货确认\n176:1. 拣货管理页面“拣货单号”列点击目标拣货单号（蓝色单号，穿透），跳转进拣货任务分配。\n178:2. 点击【拣货确认】按钮，输入拣货信息后点击【确认】按钮，完成拣货确认操作。\n182:# 2.3 波次管理\n190:# 2.3.1 新增波次单\n200:1. 点击【新增波次单】按钮或“出库订单”点击【生成波次】按钮。\n202:2. 抽屉弹窗“新增波次任务”，填写波次生成的条件设置。\n204:3. 点击【保存】按钮，二次弹窗点击【确定】按钮，完成新增波次单操作。\n206:4. 波次管理列表显示新增的波次单信息。\n212:# 2.3.2 分配\n214:2.3.2 分配波次单的分配和出库单的分配操作一样，具体详见 2.1.5。波次单多了一个“拣货波次号”字段，其意义是自动分配或手动分配时，若分配结果 loc+lot+sku 一致则合并拣货任务在一个拣...\n218:# 2.4 二次分拣\n224:# 2.4.1 发起二次分拣/分拣\n232:1. “二次分拣”页列表操作列中，点击目标分拣单行的【分拣】按钮。\n234:2. 跳转进“波次拣货任务明细”，点击操作列【分拣】按钮。\n236:3. 输入“实分拣数”后点击【确认】按钮。若“实分拣数”<"拣货数EA"则重复操作直至完全分拣。\n238:4. 完全分拣后，【分拣】按钮消失，客户tab显示件数已满，完成分拣操作。\n246:# 2.5 出库复核\n248:2.5 出库复核作为质量管控关键环节，在商品出库前进行最终质检，通过人工或设备（如扫码枪）核对订单与商品的数量、批次等信息，确保“零差错”出库，是拦截错发、漏发的最后一道防线。通俗来说像快递员送件前的...\n252:# 2.5.1 发起复核/复核\n254:2.5.1 发起复核/复核出库单拣和出库波次经拣货后都流传到出库复核中。初始数据默认复核状态“待复核”，显示【复核】按钮。若复核部分数据状态更显为“部分复核”，全部复核完成后则状态更替为“完全复核”且...\n260:1. “出库复核”页操作列点击【复核】按钮跳转进复核详情页。2.点击“未复核商品”操作列的【计件】按钮开始复核。3.弹窗“复核确认”输入“实复核数”并点击【确定】按钮。若实复核数<应复核数，则重复操作...\n262:4. 完全复核后【计件】按钮小时，状态“完全复核”，完成复核操作。\n268:# 2.6出库打包\n270:2.6 出库打包商品出库前的最后一道物理处理环节，用于安全封装已复核的商品，并生成物流面单，确保货物在运输过程中完好无损且信息准确可追溯。出库复核完全复核后的订单会流转到出库打包“未打包”tab。全部...\n274:# 2.6.1 打包装箱\n278:1. 点击【打包装箱】按钮。2.点击【+新增打包箱】按钮。3.点击商品行的【装箱】按钮，弹窗“装箱计数”。4.输入装箱数量，点击【确认】按钮。5.全部商品数量都装箱后，点击【生成打包任务】按钮，完成打...\n282:# 2.6.2合并打包\n290:# 2.7 发运管理\n294:# 2.7.1 分配承运商\n298:1. 点击【分配承运商】按钮。\n300:2. 弹窗“分配承运商”，填写承运商、物流单号（可先不填，发运前在补充）。\n302:3. 信息无误后点击【确认】按钮，完成分配承运商操作。\n306:# 2.7.2 发运确认\n310:# 1.点击【发运确认】按钮。\n313:2.二次弹窗阅读后点击【确定】按钮，完成发运操作。'}], 'temperature': 0.2, 'response_format': {'type': 'json_object'}}




UPDATE cwtpro.t_corp_device_road_speed_limit_config SET speed_duration = 120 WHERE speed_duration = 30;



UPDATE paas_rule.t_rule SET ql_sql = REPLACE(ql_sql,',durationSecond=30,',',durationSecond=120,') ,update_time = NOW() WHERE DESCRIPTION = 'roadSpeedLimit' AND app_code = 'SV_CWTPRO';