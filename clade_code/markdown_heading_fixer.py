#!/usr/bin/env python3
"""
Markdown Heading Fixer

A tool to intelligently correct Markdown heading formats and hierarchy using LLM analysis.
"""

import re
import json
import sys
import os
import argparse
import requests
from typing import List, Dict, Any, Tuple, Optional


def read_markdown_file(file_path: str) -> Tuple[List[str], str, str]:
    """
    Read a markdown file and return its content as lines.
    
    Args:
        file_path: Path to the markdown file
        
    Returns:
        Tuple containing:
        - List of lines in the file
        - Line ending style ('\n', '\r\n', etc.)
        - File encoding
    """
    # Detect file encoding and line endings
    encoding = 'utf-8'  # Default encoding
    try:
        with open(file_path, 'rb') as f:
            raw_content = f.read()
            
        # Try to detect encoding
        try:
            import chardet
            detected = chardet.detect(raw_content)
            if detected['confidence'] > 0.7:
                encoding = detected['encoding']
        except ImportError:
            pass  # Fallback to utf-8 if chardet is not available
        
        # Detect line ending
        if b'\r\n' in raw_content:
            line_ending = '\r\n'
        elif b'\n' in raw_content:
            line_ending = '\n'
        else:
            line_ending = os.linesep  # Fallback to system default
            
        # Decode content with detected encoding
        content = raw_content.decode(encoding)
        lines = content.splitlines()
        
        return lines, line_ending, encoding
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        sys.exit(1)


def extract_heading_candidates(lines: List[str]) -> List[Dict[str, Any]]:
    """
    Extract candidate heading lines from markdown content.
    
    Args:
        lines: List of lines from markdown file
        
    Returns:
        List of dictionaries with candidate heading line information
    """
    pattern = r'^#{1,6}.*|' r'^[0-9零一二三四五六七八九十\d].*'  
    candidates = []
    for i, line in enumerate(lines):
        if re.match(pattern, line):
            candidates.append({
                "line_num": i + 1,  # 1-indexed line numbers
                "content": line,
                "position": i      # 0-indexed position for internal use
            })
    
    return candidates


def prepare_llm_input(candidates: List[Dict[str, Any]], max_heading_length: int = 100) -> Dict[str, Any]:
    """
    Prepare input data for LLM analysis.
    
    Args:
        candidates: List of candidate heading lines
        max_heading_length: Maximum length of heading content to include
        
    Returns:
        Dictionary formatted for LLM input
    """
    # Create minimal input structure - just line numbers and truncated content
    input_lines = []
    for c in candidates:
        # Strip leading '#' and whitespace for cleaner LLM input
        content = c['content'].lstrip('#').strip()
        
        # Truncate long headings to prevent 504 errors
        if len(content) > max_heading_length:
            content = content[:max_heading_length] + "..."
        input_lines.append(content)
    
    # Construct the prompt for LLM analysis
    llm_input = {
        "task_description": """标题修正任务:修复Markdown标题层级，遵循以下规则:
1.格式:识别真正标题行、伪标题和无效标题
2.层级:h1-h6连续不跳级，首行必为h1，同级缩进相同
3.处理:"#数字、内容"合理转换，保留"#数.内容"的数字，删除空标题，对于标题中没有数字的，视为无效标题，按照正文处理
4、content里面的行号不需要用:添加到内容中
5.输出:直接返回JSON格式:
{"corrections":[{"line_number":行号,"original_line":"原始内容","corrected_line":"修正内容","reason":"原因"}],"structure":[{"level":层级,"content":"内容","line_num":行号}]}""",
        "input_lines": "\n".join(input_lines)
    }
    
    return llm_input


def analyze_with_llm(llm_input: Dict[str, Any], config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Send data to LLM for analysis and receive correction suggestions.
    
    Args:
        llm_input: Formatted input for LLM
        config: Configuration options for the LLM provider
        
    Returns:
        LLM response with correction information
    """
    if config is None:
        config = {
            "provider": "mock",
            "api_key": None,
            "base_url": None,
            "model": None
        }
    
    try:
        provider = config.get("provider", "mock")
        
        print("Sending data to LLM for analysis...")
        
        if provider == "openai":
            # OpenAI integration using requests
            try:
                api_key = config.get("api_key")
                base_url = config.get("base_url", "https://api.openai.com/v1")
                model = config.get("model", "gpt-4")
                
                # Ensure base_url ends with a trailing slash if not pointing to a specific endpoint
                if not base_url.endswith("/"):
                    base_url += "/"
                
                # Define the endpoint URL
                endpoint = f"{base_url}chat/completions"
                
                # Prepare headers
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                }
                
                # Prepare the request payload
                payload = {
                    "model": model,
                    "messages": [
                        {"role": "system", "content": llm_input["task_description"]},
                        {"role": "user", "content": llm_input["input_lines"] +"/no_think"}
                    ],
                    "temperature": 0.2,
                    "response_format": {"type": "json_object"}
                }
                print("payload",payload)
                # Make the API call using requests
                response = requests.post(
                    url=endpoint,
                    headers=headers,
                    json=payload,
                    timeout=600
                )
                
                # Check for successful response
                response.raise_for_status()
                
                # Parse response
                response_data = response.json()
                print("response_data",response_data)
                
                try:
                    # Try to get the content as JSON directly
                    llm_response = response_data["choices"][0]["message"]["reasoning_content"]
                    if isinstance(llm_response, dict):
                        # If it's already a dict, no need to parse
                        return extract_minimal_content(llm_response)
                    else:
                        # Otherwise, parse the JSON string
                        return extract_minimal_content(json.loads(llm_response))
                except (KeyError, json.JSONDecodeError):
                    # Fallback to parsing the content as JSON
                    llm_response = json.loads(response_data["choices"][0]["message"]["reasoning_content"])
                    return extract_minimal_content(llm_response)
                
            except requests.exceptions.RequestException as e:
                print(f"Error during OpenAI API request: {e}")
                sys.exit(1)
            except Exception as e:
                print(f"Error processing OpenAI API response: {e}")
                sys.exit(1)
                
        elif provider == "anthropic":
            # Anthropic integration using requests
            try:
                api_key = config.get("api_key")
                base_url = config.get("base_url", "https://api.anthropic.com")
                model = config.get("model", "claude-3-opus-20240229")
                
                # Ensure base_url ends with a trailing slash if not pointing to a specific endpoint
                if not base_url.endswith("/"):
                    base_url += "/"
                
                # Define the endpoint URL
                endpoint = f"{base_url}v1/messages"
                
                # Prepare headers
                headers = {
                    "Content-Type": "application/json",
                    "X-Api-Key": api_key,
                    "anthropic-version": "2023-06-01"
                }
                
                # Prepare the request payload
                payload = {
                    "model": model,
                    "system": llm_input["task_description"],
                    "max_tokens": 4000,
                    "messages": [
                        {"role": "user", "content": llm_input["input_lines"]+ "/no_think"}
                    ],
                    "temperature": 0.2
                }
                
                # Make the API call using requests
                response = requests.post(
                    endpoint,
                    headers=headers,
                    json=payload
                )
                
                # Check for successful response
                response.raise_for_status()
                
                # Parse response
                response_data = response.json()
                try:
                    # Try different formats of Anthropic API responses
                    if "content" in response_data and isinstance(response_data["content"], list):
                        text_content = response_data["content"][0]["text"]
                        if text_content.startswith("{") and text_content.endswith("}"):
                            llm_response = json.loads(text_content)
                            return extract_minimal_content(llm_response)
                except (KeyError, json.JSONDecodeError):
                    pass
                
                # Fallback to regular parsing
                llm_response = json.loads(response_data["content"][0]["text"])
                return extract_minimal_content(llm_response)
                
            except requests.exceptions.RequestException as e:
                print(f"Error during Anthropic API request: {e}")
                sys.exit(1)
            except Exception as e:
                print(f"Error processing Anthropic API response: {e}")
                sys.exit(1)
        
        else:  # mock provider or fallback
            # Mock response for demonstration
            # First parse the input lines into a list of dictionaries
            mock_candidates = []
            for line in llm_input['input_lines'].splitlines():
                if not line.strip():
                    continue
                parts = line.split(":", 1)
                if len(parts) == 2:
                    line_num = int(parts[0])
                    content = parts[1]
                    mock_candidates.append({"line_num": line_num, "content": content})
                
            mock_response = {
                "corrections": [],
                "structure": []
            }
            
            # Process each candidate
            for i, candidate in enumerate(mock_candidates):
                line_num = candidate["line_num"]
                content = candidate["content"]
                
                # Simple logic for mock corrections
                if content.strip().startswith('#'):
                    # First line should be h1
                    if i == 0:
                        level = 1
                        corrected = "# " + content.lstrip("#").strip()
                    else:
                        # Count the number of # symbols at the beginning
                        level = min(len(content) - len(content.lstrip('#')), 6)
                        
                        # Ensure proper spacing after #
                        if not content.lstrip('#').startswith(' '):
                            corrected = '#' * level + ' ' + content.lstrip('#')
                        else:
                            # Adjust heading level (in a real implementation, this would be based on LLM analysis)
                            # For mock, we'll just keep level 2 for all subsequent headings
                            level = 2
                            corrected = '#' * level + ' ' + content.lstrip('#').strip()
                else:
                    # For non-heading formats that might be mistaken as headings
                    level = 2
                    corrected = "## " + content
                
                mock_response["corrections"].append({
                    "line_number": line_num,
                    "corrected_line": corrected
                })
                
                mock_response["structure"].append({
                    "level": level,
                    "line_num": line_num
                })
            
            return extract_minimal_content(mock_response)
        
    except Exception as e:
        print(f"Error during LLM analysis: {e}")
        sys.exit(1)


def update_markdown_content(
    original_lines: List[str],
    corrections: List[Dict[str, Any]],
    line_ending: str
) -> str:
    """
    Apply corrections to the original markdown content.
    
    Args:
        original_lines: Original file content as lines
        corrections: Corrections from LLM analysis
        line_ending: Line ending style to use
        
    Returns:
        Updated file content as string
    """
    updated_lines = original_lines.copy()
    
    # Sort corrections by line number in descending order
    sorted_corrections = sorted(
        corrections, 
        key=lambda x: x["line_number"], 
        reverse=True
    )
    
    lines_to_delete = []
    
    # Apply corrections
    for correction in sorted_corrections:
        line_num = correction["line_number"]
        position = line_num - 1  # Convert to 0-indexed
        
        if position < 0 or position >= len(updated_lines):
            continue
        
        corrected_line = correction.get("corrected_line")
        
        if corrected_line is None:
            lines_to_delete.append(position)
        else:
            # Remove potential line number prefixes from the corrected line
            cleaned_line = re.sub(r'^\d+:', '', corrected_line).lstrip()
            updated_lines[position] = cleaned_line
    
    # Remove lines marked for deletion
    for position in sorted(lines_to_delete, reverse=True):
        del updated_lines[position]
    
    # Join the lines with the original line ending
    updated_content = line_ending.join(updated_lines)
    
    # Ensure the file ends with a newline
    if updated_lines and not updated_content.endswith(line_ending):
        updated_content += line_ending
    
    return updated_content


def extract_minimal_content(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract only the most essential information from the LLM response.
    
    Args:
        response: The full LLM response
        
    Returns:
        Minimized response with only essential data
    """
    minimal_response = {"corrections": [], "structure": []}
    
    # Extract minimal correction information
    if "corrections" in response:
        for correction in response["corrections"]:
            minimal_correction = {
                "line_number": correction["line_number"],
                "corrected_line": correction.get("corrected_line")
            }
            minimal_response["corrections"].append(minimal_correction)
    
    # Extract minimal structure information
    if "structure" in response:
        for item in response["structure"]:
            minimal_item = {
                "level": item.get("level"),
                "line_num": item.get("line_num")
            }
            minimal_response["structure"].append(minimal_item)
    
    return minimal_response


def main() -> None:
    """Main function to process markdown files."""
    parser = argparse.ArgumentParser(
        description="Markdown Heading Fixer - A tool to intelligently correct heading formats and hierarchy"
    )
    parser.add_argument(
        "file_path", 
        help="Path to the markdown file to process"
    )
    parser.add_argument(
        "-o", "--output",
        help="Output file path. If not provided, the input file will be modified in-place"
    )
    parser.add_argument(
        "-d", "--dry-run",
        action="store_true",
        help="Show corrections without modifying the file"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Show detailed output about the corrections"
    )
    parser.add_argument(
        "--max-heading-length",
        type=int,
        default=100,
        help="Maximum length of heading content to include in API request (default: 100)"
    )
    
    # LLM API configuration
    llm_group = parser.add_argument_group('LLM Configuration')
    llm_group.add_argument(
        "--llm-provider",
        choices=["openai", "anthropic", "mock"],
        default="openai",
        help="LLM provider to use for analysis (default: mock)"
    )
    llm_group.add_argument(
        "--api-key",
        default="sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db",
        help="API key for the LLM provider"
    )
    llm_group.add_argument(
        "--base-url",
        default="http://ai.ai.iot.chinamobile.com/imaas/v1",
        help="Base URL for the LLM API (for custom endpoints)"
    )
    llm_group.add_argument(
        "--model",
        default="DeepSeek-R1",
        help="Model to use for analysis (e.g., gpt-4, claude-3-opus-20240229)"
    )
    
    args = parser.parse_args()
    
    # Check if the file exists
    if not os.path.isfile(args.file_path):
        print(f"Error: File {args.file_path} does not exist")
        sys.exit(1)
    
    # Read the markdown file
    lines, line_ending, encoding = read_markdown_file(args.file_path)
    
    # Extract heading candidates
    candidates = extract_heading_candidates(lines)
    
    if not candidates:
        print("No heading candidates found in the file.")
        sys.exit(0)
    
    if args.verbose:
        print(f"Found {len(candidates)} heading candidates:")
        for candidate in candidates:
            print(f"  Line {candidate['line_num']}: {candidate['content']}")
    
    # Prepare data for LLM
    llm_input = prepare_llm_input(candidates, max_heading_length=args.max_heading_length)
    
    # Configure LLM
    llm_config = {
        "provider": args.llm_provider,
        "api_key": args.api_key,
        "base_url": args.base_url,
        "model": args.model
    }
    
    # Send to LLM for analysis
    llm_response = analyze_with_llm(llm_input, config=llm_config)
    # Process corrections
    if "corrections" not in llm_response or not llm_response["corrections"]:
        print("No corrections needed.")
        sys.exit(0)
    
    if args.verbose or args.dry_run:
        print("\nCorrections to be applied:")
        for correction in llm_response["corrections"]:
            line_num = correction["line_number"]
            corrected = correction.get("corrected_line", "DELETE")
            print(f"  Line {line_num}: {corrected}")
            
    if args.verbose:
        print("\nHeading structure:")
        for heading in llm_response.get("structure", []):
            level = heading.get("level")
            line_num = heading.get("line_num")
            print(f"  Level {level} (Line {line_num})")
    
    if not args.dry_run:
        # Update content based on corrections
        updated_content = update_markdown_content(
            lines, 
            llm_response["corrections"],
            line_ending
        )
        
        # Determine output path
        output_path = args.output if args.output else args.file_path
        
        # Write to output file
        try:
            with open(output_path, 'w', encoding=encoding, newline='') as f:
                f.write(updated_content)
            print(f"Successfully updated markdown headings in {output_path}")
        except Exception as e:
            print(f"Error writing to file {output_path}: {e}")
            sys.exit(1)
    else:
        print("Dry run completed. No changes were made to the file.")


if __name__ == "__main__":
    main()