import requests
import os
import json
import time
import zipfile

# --- 配置 ---
API_TOKEN = "eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFM1MTIifQ.eyJqdGkiOiI2MjQwMzY5NCIsInJvbCI6IlJPTEVfUkVHSVNURVIiLCJpc3MiOiJPcGVuWExhYiIsImlhdCI6MTc1MjU3NTQxMiwiY2xpZW50SWQiOiJsa3pkeDU3bnZ5MjJqa3BxOXgydyIsInBob25lIjoiIiwib3BlbklkIjpudWxsLCJ1dWlkIjoiMTcwZjlkOGEtNjdkMC00Mzk5LWFjNTgtM2UzZTA3MzYxNWNkIiwiZW1haWwiOiIiLCJleHAiOjE3NTM3ODUwMTJ9.YnI5JbSPDoN_WFKjYHQDftf5_OyDIvzVtE6ArjMUifn1MABsPCtuLhDobUn1oILLjRn27bHzyIDD5GSpzWlKlQ"
BASE_URL = "https://mineru.net/api/v4"

def get_upload_urls(file_paths, enable_formula=True, language="en", enable_table=True):
    url = f"{BASE_URL}/file-urls/batch"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {API_TOKEN}'
    }

    files_metadata = []
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"错误：文件 '{file_path}' 不存在。")
            return None, None
        files_metadata.append({
            "name": os.path.basename(file_path),
            "is_ocr": file_path.lower().endswith('.pdf'),
            "data_id": os.path.basename(file_path)
        })

    payload = {
        "enable_formula": enable_formula,
        "language": language,
        "enable_table": enable_table,
        "files": files_metadata,
        "model_version": "v2"
    }

    print("1. 正在申请文件上传链接...")
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=30)
        response.raise_for_status()

        result = response.json()
        if result.get("code") == 0:
            batch_id = result["data"]["batch_id"]
            urls_list = result["data"]["file_urls"]
            urls_dict = {os.path.basename(file_path): url for file_path, url in zip(file_paths, urls_list)}
            print(f"  -> 成功获取上传链接。批次ID: {batch_id}")
            return batch_id, urls_dict
        else:
            print(f"  -> 获取上传链接失败：{result.get('msg')}")
            return None, None

    except requests.exceptions.RequestException as e:
        print(f"  -> 请求失败: {e}")
        return None, None

def upload_files_to_presigned_urls(file_paths, presigned_urls):
    print("\n2. 正在上传文件...")
    all_successful = True
    for file_path in file_paths:
        filename = os.path.basename(file_path)
        upload_url = presigned_urls.get(filename)

        if not upload_url:
            print(f"  -> 未找到上传链接：{filename}")
            all_successful = False
            continue

        print(f"  -> 上传中: {filename}")
        try:
            with open(file_path, 'rb') as f:
                upload_response = requests.put(upload_url, data=f, timeout=120)
            if upload_response.status_code == 200:
                print(f"    - 上传成功")
            else:
                print(f"    - 上传失败：{upload_response.status_code}")
                all_successful = False
        except requests.exceptions.RequestException as e:
            print(f"    - 上传异常：{e}")
            all_successful = False

    return all_successful

def poll_and_get_results(batch_id, file_names, output_dir="results"):
    print(f"\n3. 正在轮询批次ID '{batch_id}' 的解析结果...")
    url = f"{BASE_URL}/extract-results/batch/{batch_id}"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {API_TOKEN}'
    }

    max_retries = 20
    retry_interval = 10

    for i in range(max_retries):
        print(f"  -> 第 {i+1}/{max_retries} 次尝试获取结果...")
        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                data = result.get("data", {})
                status = data.get("extract_result")[0]["state"]
                print(f"    - 当前状态: {status}")

                if status == 'done':
                    full_zip_url = data.get("extract_result")[0]["full_zip_url"]
                    if not full_zip_url:
                        print("  -> 错误：未找到 full_zip_url")
                        return False

                    zip_response = requests.get(full_zip_url, stream=True, timeout=120)
                    zip_response.raise_for_status()

                    os.makedirs(output_dir, exist_ok=True)
                    temp_zip_path = os.path.join(output_dir, f"{batch_id}_result.zip")

                    with open(temp_zip_path, 'wb') as f:
                        for chunk in zip_response.iter_content(chunk_size=8192):
                            f.write(chunk)

                    print(f"  -> ZIP文件已下载: {temp_zip_path}")

                    # 重命名为上传文件名.zip
                    for file_path in file_names:
                        base_name = os.path.splitext(os.path.basename(file_path))[0]
                        renamed_path = os.path.join(output_dir, f"{base_name}.zip")
                        with open(temp_zip_path, 'rb') as src, open(renamed_path, 'wb') as dst:
                            dst.write(src.read())
                        print(f"  -> 保存为: {renamed_path}")
                        os.remove(temp_zip_path)

                    return True

                elif status == 'failed':
                    print("  -> 解析任务失败")
                    return False

            else:
                print(f"  -> 获取状态失败: {result.get('msg')}")

        except Exception as e:
            print(f"  -> 请求异常: {e}")

        print(f"    - 等待 {retry_interval} 秒后重试...")
        time.sleep(retry_interval)

    print("  -> 超过最大轮询次数")
    return False

def main(files_to_upload,output_dir):
    if not files_to_upload:
        print("没有指定文件，将自动生成 demo.pdf 测试")
        if not os.path.exists("demo.pdf"):
            with open("demo.pdf", "w") as f:
                f.write("This is a demo PDF.")
        files_to_upload.append("demo.pdf")

    batch_id, urls_to_upload = get_upload_urls(files_to_upload)
    if not batch_id or not urls_to_upload:
        print("终止：上传链接获取失败")
        return

    if not upload_files_to_presigned_urls(files_to_upload, urls_to_upload):
        print("终止：上传失败")
        return

    print("上传成功！正在等待处理完成并获取结果...")
    poll_and_get_results(batch_id, file_names=files_to_upload,output_dir=output_dir)

if __name__ == "__main__":
    files_to_upload = [
        "/Volumes/DISKO1/CMIOT/同创知识库/操作手册/仓库管理操作手册_new.docx"
    ]
    output_dir = "/Volumes/DISKO1/CMIOT/同创知识库/操作手册/result"
    if API_TOKEN == "你的API_TOKEN":
        print("请设置 API_TOKEN 后再运行")
    else:
        main(files_to_upload,output_dir=output_dir)
