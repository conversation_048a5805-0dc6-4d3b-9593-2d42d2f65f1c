import argparse
import os
import re

def process_markdown_file(file_path, base_url):
    """
    Processes a single markdown file to remove blank lines and replace image paths.

    :param file_path: Path to the markdown file.
    :param base_url: The base URL to prepend to image paths.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Remove blank lines
        non_blank_lines = [line for line in lines if line.strip()]

        # Replace image paths
        processed_lines = []
        for line in non_blank_lines:
            # The regex looks for ![](images/...) and replaces it
            new_line = re.sub(r'!\[\]\(images/', f'![]({base_url}/images/', line)
            processed_lines.append(new_line)

        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(processed_lines)

        print(f"Successfully processed {file_path}")

    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"An error occurred while processing {file_path}: {e}")

def main():
    """
    Main function to parse arguments and process files.
    """
    # Define the list of markdown files to be processed here
    file_list = [
        "/Volumes/DISKO1/CMIOT/同创知识库/操作手册/result/仓库管理操作手册_new/仓库管理操作手册_new.md"
    ]

    parser = argparse.ArgumentParser(description="Process markdown files to remove blank lines and update image links.")
    parser.add_argument('--base_url', required=True, help="The base URL for image links.")

    args = parser.parse_args()

    for file_path in file_list:
        if os.path.isfile(file_path) and file_path.endswith('.md'):
            process_markdown_file(file_path, args.base_url)
        else:
            print(f"Skipping invalid or non-markdown file: {file_path}")

if __name__ == "__main__":
    main()
