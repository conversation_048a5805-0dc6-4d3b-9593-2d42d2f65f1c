#!/usr/bin/env python3
"""
Test script for the Markdown Heading Fixer
"""

import os
import subprocess
import tempfile

def create_test_file(content, filename):
    """Create a test markdown file with given content."""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

def run_fixer(input_file, *args):
    """Run the markdown fixer with given arguments."""
    cmd = ['python', 'clade_code/markdown_heading_fixer.py', input_file] + list(args)
    result = subprocess.run(cmd, capture_output=True, text=True)
    return result.returncode, result.stdout, result.stderr

def test_basic_functionality():
    """Test basic heading correction functionality."""
    print("Testing basic functionality...")
    
    test_content = """# 主标题

## 1. 第一章

### 1.1 子章节

2. 第二章

这是一个标题

#### 2.1.1 深层标题

第三章

5、第五章

## 总结
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        test_file = f.name
    
    try:
        # Test dry run
        returncode, stdout, stderr = run_fixer(test_file, '--dry-run', '--verbose')
        
        if returncode == 0:
            print("✓ Dry run test passed")
            print(f"Found corrections in output: {'corrections to be applied' in stdout}")
        else:
            print(f"✗ Dry run test failed: {stderr}")
        
        # Test actual fixing
        output_file = test_file.replace('.md', '_fixed.md')
        returncode, stdout, stderr = run_fixer(test_file, '-o', output_file, '--verbose')
        
        if returncode == 0 and os.path.exists(output_file):
            print("✓ File fixing test passed")
            with open(output_file, 'r', encoding='utf-8') as f:
                fixed_content = f.read()
            print(f"Fixed file contains markdown headers: {'##' in fixed_content}")
            os.unlink(output_file)
        else:
            print(f"✗ File fixing test failed: {stderr}")
            
    finally:
        if os.path.exists(test_file):
            os.unlink(test_file)

def test_edge_cases():
    """Test edge cases and error handling."""
    print("\nTesting edge cases...")
    
    # Test empty file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
        f.write("")
        empty_file = f.name
    
    try:
        returncode, stdout, stderr = run_fixer(empty_file, '--dry-run')
        if "No heading candidates found" in stdout:
            print("✓ Empty file test passed")
        else:
            print("✗ Empty file test failed")
    finally:
        os.unlink(empty_file)
    
    # Test non-existent file
    returncode, stdout, stderr = run_fixer('non_existent_file.md', '--dry-run')
    if returncode != 0 and "does not exist" in stdout:
        print("✓ Non-existent file test passed")
    else:
        print("✗ Non-existent file test failed")

def test_different_formats():
    """Test different heading formats."""
    print("\nTesting different heading formats...")
    
    test_content = """文档标题

1. 第一章
1.1 子章节
1.1.1 子子章节

二、第二章

第三章 概述

4、第四章

Chapter 5 English Title

## 已有的markdown标题

### 子标题

总结部分
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        test_file = f.name
    
    try:
        returncode, stdout, stderr = run_fixer(test_file, '--dry-run', '--verbose')
        
        if returncode == 0:
            print("✓ Different formats test passed")
            # Check if various formats were detected
            formats_detected = [
                "第一章" in stdout,
                "第二章" in stdout,
                "第三章" in stdout,
                "第四章" in stdout,
                "总结" in stdout
            ]
            print(f"Detected formats: {sum(formats_detected)}/5")
        else:
            print(f"✗ Different formats test failed: {stderr}")
            
    finally:
        os.unlink(test_file)

def main():
    """Run all tests."""
    print("Markdown Heading Fixer Test Suite")
    print("=" * 40)
    
    # Change to the correct directory
    if not os.path.exists('clade_code/markdown_heading_fixer.py'):
        print("Error: markdown_heading_fixer.py not found in clade_code/")
        return
    
    test_basic_functionality()
    test_edge_cases()
    test_different_formats()
    
    print("\n" + "=" * 40)
    print("Test suite completed!")

if __name__ == "__main__":
    main()
