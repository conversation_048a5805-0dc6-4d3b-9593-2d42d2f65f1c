{"version": 3, "names": ["_index", "require", "buildUndefinedNode", "unaryExpression", "numericLiteral"], "sources": ["../../src/builders/productions.ts"], "sourcesContent": ["import { numericLiteral, unaryExpression } from \"./generated/index.ts\";\n\nexport function buildUndefinedNode() {\n  return unaryExpression(\"void\", numericLiteral(0), true);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEO,SAASC,kBAAkBA,CAAA,EAAG;EACnC,OAAO,IAAAC,sBAAe,EAAC,MAAM,EAAE,IAAAC,qBAAc,EAAC,CAAC,CAAC,EAAE,IAAI,CAAC;AACzD", "ignoreList": []}